# ============================================================================
# SPACE-OAAL 仿真环境配置文件
# ============================================================================

# ============================================================================
# 系统架构参数 (System Architecture Parameters)
# ============================================================================
system:
  # 基础系统配置
  num_users: 420                    # 地面用户终端数量
  num_leo_satellites: 72            # LEO卫星数量
  num_cloud_centers: 5              # 云计算中心数量
  num_policy_domains: 24            # 策略域数量

  # 轨道参数
  leo_altitude_m: 1200000           # LEO卫星轨道高度 (m)
  earth_radius_m: 6371000           # 地球半径 (m)
  
  # 时间参数
  timeslot_duration_s: 3           # 时隙持续时间 (秒)
  total_timeslots: 1500             # 总时隙数
  # 可见距离
  visibility_threshold_m: 5000000   # 卫星间可见性阈值距离 (m)
  visibility_earth_m: 2500000       # 地面站-卫星可见性阈值距离 (m)
  cloud_visibility_threshold_m: 3300000  # 云中心-卫星可见性阈值距离 (m)

# ============================================================================
# GPU加速配置 (GPU Acceleration Configuration)
# ============================================================================
gpu:
  enable_gpu_acceleration: true        # 是否启用GPU加速 (true/false)
  device: null                         # 指定设备 ('cuda', 'cpu', null为自动选择)
  batch_size_threshold: 10             # 启用GPU批量计算的最小任务数
  fallback_to_cpu: true               # GPU失败时是否回退到CPU

# ============================================================================
# 通信模型参数 (Communication Model Parameters)
# ============================================================================
communication:
  # 射频通信参数
  rf_noise_power_w: 1.0e-13         # 射频信道背景噪声 (W)
  rf_carrier_freq_hz: 14000000000    # 射频载波频率 (Hz)
  rician_k_factor: 10               # 莱斯K因子

  # 用户-卫星上行链路
  b_us_hz: 1000000000                # 用户-卫星上行带宽 (Hz)
  p_u_w: 5                          # 用户终端发射功率 (W)

  # 卫星-用户下行链路  
  b_su_hz: 120000000                # 卫星-用户下行带宽 (Hz)
  p_su_w: 10                        # 卫星对用户发射功率 (W)

  # 卫星-云中心下行链路
  b_sc_hz: 150000000                # 卫星-云下行带宽 (Hz)
  p_sc_w: 15                        # 卫星对云发射功率 (W)

  # 云中心-卫星上行链路
  b_cs_hz: 180000000                # 云-卫星上行带宽 (Hz) 
  p_c_w: 50                         # 云中心发射功率 (W)

  # 星间激光链路参数
  isl_tra_rate: 50000000000         # 星间链路传输速率 (bps)

  # 物理常数
  light_speed_ms: 299792458         # 光速 (m/s)

  # 单位转换
  mb_to_bits: 8388608               # MB到bits转换系数
  
  # 通信链路参数
  antenna_gain_db: 33.0             # 天线增益 (dB)
  system_noise_dbm_hz: -174.0       # 系统噪声功率密度 (dBm/Hz)
  implementation_loss_db: 2.0       # 实现损耗 (dB)
  rain_fade_margin_db: 6.0          # 雨衰余量 (dB)
  coding_efficiency: 0.7            # 编码效率
  
  # 协议参数
  max_retries: 3                    # 最大重传次数
  timeout_threshold_ms: 1000        # 通信超时阈值 (ms)
  processing_delay_ms: 5.0          # 处理延迟 (ms)

# ============================================================================
# 计算模型参数 (Computation Model Parameters)
# ============================================================================
computation:
  # LEO卫星计算资源
  f_leo_hz: 50000000000            # LEO卫星CPU频率 (Hz) - 

  # 云计算资源
  f_cloud_hz: 100000000000          # 云中心CPU频率 (Hz) - 100GHz

  # 能效参数
  zeta_leo: 1.0e-10                 # LEO卫星能效系数 (J/cycle) - 调整到更合理的值
  zeta_cloud: 1.0e-11               # 云中心能效系数 (J/cycle)

  # 电池和能量约束
  leo_battery_capacity_j: 3600000   # LEO卫星电池容量 (J)
  leo_solar_power_w: 5000           # LEO卫星太阳能板功率 (W)
  energy_threshold_ratio: 0.2       # 能量阈值比例
  
  # 处理开销和其他参数
  processing_overhead_ratio: 0.05   # 处理开销比例
  idle_power_w: 10.0                # 空闲功耗 (W)
  max_parallel_tasks: 100           # 最大并行任务数
  cpu_allocation_levels: [1, 2, 3, 4, 5, 10, 15, 20, 25, 30, 40, 50, 60, 70, 80, 90, 100]  # CPU分配级别
  min_cpu_allocation: 1             # 最小CPU分配（1%可以处理100个任务）
  max_cpu_allocation: 100           # 最大CPU分配
  cpu_allocation_step: 1            # CPU分配步长
  default_drop_penalty: 100.0       # 默认丢弃惩罚
  penalty_multiplier: 2.0           # 惩罚乘数

  

# ============================================================================
# 排队模型参数 (Queuing Model Parameters)
# ============================================================================
queuing:
  # 动态优先级评分权重
  w_priority: 1.0                   # 优先级因子权重
  w_urgency: 2.0                    # 紧迫性因子权重
  w_cost: 0.5                       # 成本因子权重

  # 队列管理参数
  max_queue_size: 100               # 最大队列长度
  cloud_queue_multiplier: 10        # 云中心队列大小倍数
  queue_timeout_s: 300              # 队列超时时间 (秒)
  epsilon_urgency: 1.0e-6           # 紧迫性计算防零参数

  # 调度参数
  scheduling_interval_s: 1          # 调度间隔 (秒)
  preemption_enabled: true          # 是否启用抢占
  priority_aging_factor: 0.01       # 优先级老化因子


# ============================================================================
# 观测空间归一化参数 (Observation Space Normalization Parameters)
# ============================================================================
observation:
  # 位置信息归一化参数
  max_latitude_deg: 90.0            # 最大纬度（度）
  max_longitude_deg: 180.0          # 最大经度（度）
  max_altitude_km: 2000.0           # 最大高度（公里）
  
  # 任务信息归一化参数
  max_queue_length: 10.0            # 任务队列长度归一化基数
  max_task_priority: 10.0           # 最大任务优先级
  max_task_urgency: 100.0           # 最大任务紧急度
  

# ============================================================================
# 负载均衡模型参数 (Load Balancing Model Parameters)
# ============================================================================
load_balancing:
  # 负载计算权重
  w_cpu: 0.7                        # CPU利用率权重
  w_queue: 0.3                      # 队列长度权重

# ============================================================================
# 任务生成模型参数 (Task Generation Model Parameters)
# ============================================================================
task_generation:
  # Lambda parameters for different geography types
  ocean_lambda: 0.8                 # 海洋站点任务生成率
  land_large_lambda: 3.5       # 大型陆地站点任务生成率
  land_medium_lambda: 2.5        # 中型陆地站点任务生成率
  land_small_lambda: 1.6           # 小型陆地站点任务生成率

  # Task type probabilities for different functional types
  normal_probabilities: [0.2, 0.6, 0.2]           # [实时, 普通, 计算密集] 概率
  industrial_probabilities: [0.2, 0.2, 0.6]       # 工业区域任务类型概率
  delay_sensitive_probabilities: [0.6, 0.2, 0.2]  # 延迟敏感区域任务类型概率

  # Task parameters for different task types
  type1_data_range: [10.0, 20.0]    # 实时任务数据大小范围 (MB)
  type1_complexity: 100             # 实时任务复杂度 (cycles/bit)
  type1_deadline_offset: 5.0        # 实时任务截止时间偏移 (秒)

  type2_data_range: [20.0, 40.0]    # 普通任务数据大小范围 (MB)
  type2_complexity: 150             # 普通任务复杂度 (cycles/bit)
  type2_deadline_offset: 15.0       # 普通任务截止时间偏移 (秒)

  type3_data_range: [40.0, 60.0]   # 计算密集型任务数据大小范围 (MB)
  type3_complexity: 200             # 计算密集型任务复杂度 (cycles/bit)
  type3_deadline_offset: 35.0       # 计算密集型任务截止时间偏移 (秒)

  # Priority range
  min_priority: 1                   # 最小优先级
  max_priority: 3                   # 最大优先级
