"""
Satellite程序综合测试套件
测试SatelliteCompute和相关组件的正确性
分为单元测试、集成测试和性能测试三个层次
"""

import sys
import os
import numpy as np
import yaml
import pytest
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

# 导入被测试的模块
from src.env.satellite_cloud.satellite_compute import SatelliteCompute, DPSQScheduler
from src.env.satellite_cloud.compute_models import ComputeTask, TaskStatus, ProcessingResult
from src.env.physics_layer.task_models import Task, TaskType
from src.env.physics_layer.orbital import OrbitalUpdater, Satellite
from src.env.Foundation_Layer.time_manager import TimeContext, create_time_manager_from_config
from src.env.Foundation_Layer.logging_config import initialize_logging_and_config


class TestSatelliteUnit:
    """单元测试：测试satellite的基本功能组件"""
    
    def setup_method(self):
        """测试前的初始化"""
        # 初始化日志
        initialize_logging_and_config("src/env/physics_layer/config.yaml")
        
        # 加载配置
        with open("src/env/physics_layer/config.yaml", 'r') as f:
            self.config = yaml.safe_load(f)
        
        # 创建测试用的卫星计算器
        self.satellite_compute = SatelliteCompute(
            satellite_id=111,
            config=self.config
        )
        
        # 创建测试任务
        self.test_task = Task(
            task_id=1001,
            type_id=TaskType.NORMAL,
            data_size_mb=10.0,
            complexity_cycles_per_bit=1000,
            deadline_timestamp=100.0,
            priority=2,
            location_id=1,
            coordinates=(35.0, -108.0),
            generation_time=50.0
        )
    
    def test_dpsq_scheduler_initialization(self):
        """测试DPSQ调度器初始化"""
        scheduler = DPSQScheduler(self.config)
        
        # 验证调度器参数
        assert scheduler.w_priority > 0
        assert scheduler.w_urgency > 0
        assert scheduler.w_cost > 0
        assert scheduler.f_sat > 0
        assert scheduler.max_queue_size > 0
        
        print(f"✅ DPSQ调度器初始化成功")
        print(f"   权重配置: priority={scheduler.w_priority}, urgency={scheduler.w_urgency}, cost={scheduler.w_cost}")
    
    def test_dpsq_score_calculation(self):
        """测试DPSQ评分计算的正确性"""
        scheduler = DPSQScheduler(self.config)
        
        # 创建测试任务
        compute_task = ComputeTask.from_task(
            self.test_task,
            arrival_time=50.0,
            drop_penalty=1.0
        )
        
        # 计算评分
        current_time = 60.0
        bandwidth = 1000.0  # MB/s
        score = scheduler.calculate_score(compute_task, current_time, bandwidth)
        
        # 验证评分合理性
        assert isinstance(score, float)
        assert not np.isnan(score)
        assert not np.isinf(score)
        
        print(f"✅ DPSQ评分计算正确")
        print(f"   任务{compute_task.task_id}评分: {score:.4f}")
        
        # 测试不同优先级的评分差异
        high_priority_task = ComputeTask.from_task(
            Task(task_id=1002, type_id=TaskType.REALTIME, data_size_mb=10.0,
                 complexity_cycles_per_bit=1000, deadline_timestamp=100.0,
                 priority=1, location_id=1, coordinates=(35.0, -108.0),
                 generation_time=50.0),
            arrival_time=50.0, drop_penalty=1.0
        )
        
        high_score = scheduler.calculate_score(high_priority_task, current_time, bandwidth)
        
        # 高优先级任务应该有更高的评分
        assert high_score > score
        print(f"   高优先级任务评分: {high_score:.4f} > 普通任务: {score:.4f}")
    
    def test_satellite_compute_initialization(self):
        """测试卫星计算器初始化"""
        # 验证基本属性
        assert self.satellite_compute.satellite_id == 111
        assert self.satellite_compute.cpu_frequency > 0
        assert self.satellite_compute.max_battery > 0
        assert self.satellite_compute.battery_energy <= self.satellite_compute.max_battery
        
        # 验证调度器
        assert self.satellite_compute.scheduler is not None
        assert isinstance(self.satellite_compute.scheduler, DPSQScheduler)
        
        print(f"✅ 卫星计算器初始化成功")
        print(f"   卫星ID: {self.satellite_compute.satellite_id}")
        print(f"   CPU频率: {self.satellite_compute.cpu_frequency/1e9:.1f} GHz")
        print(f"   电池容量: {self.satellite_compute.max_battery/1e6:.1f} MJ")
    
    def test_task_addition_and_rejection(self):
        """测试任务添加和拒绝机制"""
        # 测试正常任务添加
        success = self.satellite_compute.add_task(self.test_task)
        assert success == True
        assert len(self.satellite_compute.task_queue) == 1
        
        print(f"✅ 任务添加成功，队列长度: {len(self.satellite_compute.task_queue)}")
        
        # 测试队列满时的拒绝
        max_queue = self.satellite_compute.scheduler.max_queue_size
        for i in range(max_queue):
            task = Task(
                task_id=2000+i, type_id=TaskType.NORMAL, data_size_mb=1.0,
                complexity_cycles_per_bit=100, deadline_timestamp=200.0,
                priority=3, location_id=1, coordinates=(35.0, -108.0),
                generation_time=100.0
            )
            self.satellite_compute.add_task(task)
        
        # 队列满后应该拒绝新任务
        overflow_task = Task(
            task_id=9999, type_id=TaskType.NORMAL, data_size_mb=1.0,
            complexity_cycles_per_bit=100, deadline_timestamp=200.0,
            priority=3, location_id=1, coordinates=(35.0, -108.0),
            generation_time=100.0
        )
        rejected = self.satellite_compute.add_task(overflow_task)
        assert rejected == False
        
        print(f"✅ 队列满时正确拒绝任务，队列长度: {len(self.satellite_compute.task_queue)}")
    
    def test_energy_constraint(self):
        """测试能量约束机制"""
        # 获取初始能量状态
        initial_energy = self.satellite_compute.battery_energy
        
        # 创建高能耗任务
        high_energy_task = Task(
            task_id=3001, type_id=TaskType.COMPUTE_INTENSIVE, data_size_mb=100.0,
            complexity_cycles_per_bit=10000, deadline_timestamp=200.0,
            priority=1, location_id=1, coordinates=(35.0, -108.0),
            generation_time=100.0
        )
        
        # 测试能量检查
        compute_task = ComputeTask.from_task(high_energy_task, arrival_time=100.0, drop_penalty=1.0)
        can_accept = self.satellite_compute.can_accept_task(compute_task)
        
        print(f"✅ 能量约束测试")
        print(f"   当前电池能量: {initial_energy/1e6:.2f} MJ")
        print(f"   高能耗任务可接受: {can_accept}")
        
        # 验证能量约束逻辑
        required_energy = self.satellite_compute.scheduler.zeta_leo * compute_task.complexity
        min_energy = self.satellite_compute.max_battery * self.satellite_compute.energy_threshold
        available_energy = self.satellite_compute.battery_energy - min_energy
        
        expected_result = available_energy >= required_energy
        assert can_accept == expected_result
        
        print(f"   需要能量: {required_energy/1e6:.2f} MJ")
        print(f"   可用能量: {available_energy/1e6:.2f} MJ")


class TestSatelliteIntegration:
    """集成测试：测试satellite与其他模块的协作"""
    
    def setup_method(self):
        """集成测试初始化"""
        initialize_logging_and_config("src/env/physics_layer/config.yaml")
        
        with open("src/env/physics_layer/config.yaml", 'r') as f:
            self.config = yaml.safe_load(f)
        
        # 创建时间管理器
        self.time_manager = create_time_manager_from_config(self.config)
        
        # 创建轨道更新器
        self.orbital_updater = OrbitalUpdater()
        
        # 创建卫星计算器
        self.satellite_compute = SatelliteCompute(
            satellite_id=111,
            config=self.config,
            orbital_updater=self.orbital_updater
        )
    
    def test_timeslot_processing(self):
        """测试时隙处理的完整流程"""
        print(f"\n🔄 开始时隙处理集成测试")
        
        # 选择测试时隙
        test_timeslots = [1, 100, 500]
        
        for timeslot in test_timeslots:
            print(f"\n--- 测试时隙 {timeslot} ---")
            
            # 获取时间上下文
            time_context = self.time_manager.get_context(timeslot)
            
            # 更新轨道状态
            self.orbital_updater.update_positions(time_context)
            satellites = self.orbital_updater.get_satellites_at_time(timeslot)
            
            # 验证卫星存在
            assert str(111) in satellites or "111" in satellites
            
            # 获取卫星状态
            satellite_obj = satellites.get(str(111)) or satellites.get("111")
            illuminated = satellite_obj.illuminated if satellite_obj else True
            
            # 添加测试任务
            test_task = Task(
                task_id=timeslot*1000 + 1,
                type_id=TaskType.NORMAL,
                data_size_mb=5.0,
                complexity_cycles_per_bit=500,
                deadline_timestamp=time_context.simulation_time + 30.0,
                priority=2,
                location_id=1,
                coordinates=(35.0, -108.0),
                generation_time=time_context.simulation_time
            )
            
            # 添加任务到卫星
            added = self.satellite_compute.add_task(test_task)
            
            # 处理时隙
            duration = time_context.timeslot_duration
            result = self.satellite_compute.process_timeslot(duration, illuminated)
            
            # 验证处理结果
            assert isinstance(result, ProcessingResult)
            
            print(f"   时间: {time_context.physical_time}")
            print(f"   光照状态: {illuminated}")
            print(f"   任务添加: {added}")
            print(f"   完成任务数: {len(result.completed_tasks)}")
            print(f"   队列长度: {len(self.satellite_compute.task_queue)}")
            print(f"   电池能量: {self.satellite_compute.battery_energy/1e6:.2f} MJ")
        
        print(f"✅ 时隙处理集成测试完成")


if __name__ == "__main__":
    print("🚀 开始Satellite程序综合测试")
    print("="*60)
    
    # 运行单元测试
    print("\n📋 第一部分：单元测试")
    unit_test = TestSatelliteUnit()
    unit_test.setup_method()
    
    try:
        unit_test.test_dpsq_scheduler_initialization()
        unit_test.test_dpsq_score_calculation()
        unit_test.test_satellite_compute_initialization()
        unit_test.test_task_addition_and_rejection()
        unit_test.test_energy_constraint()
        print("\n✅ 单元测试全部通过")
    except Exception as e:
        print(f"\n❌ 单元测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 运行集成测试
    print("\n📋 第二部分：集成测试")
    integration_test = TestSatelliteIntegration()
    integration_test.setup_method()
    
    try:
        integration_test.test_timeslot_processing()
        print("\n✅ 集成测试全部通过")
    except Exception as e:
        print(f"\n❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎉 Satellite程序测试完成")
